// Test script for page rename and delete operations
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000';

// Test session cookie (you'll need to get this from browser dev tools)
const SESSION_COOKIE = 'your-session-cookie-here';

async function testPageOperations() {
  console.log('🧪 Testing Page Operations...\n');

  try {
    // Test 1: List projects to get a project ID
    console.log('1. Testing project list...');
    const projectsResponse = await fetch(`${BASE_URL}/api/page_gen/project/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': SESSION_COOKIE
      },
      body: JSON.stringify({
        page: { pageNum: 1, pageSize: 10 }
      })
    });

    if (!projectsResponse.ok) {
      throw new Error(`Project list failed: ${projectsResponse.status}`);
    }

    const projectsData = await projectsResponse.json();
    console.log('✅ Projects found:', projectsData.projects.length);

    if (projectsData.projects.length === 0) {
      console.log('❌ No projects found. Please create a project first.');
      return;
    }

    const projectId = projectsData.projects[0].id;
    console.log('📁 Using project ID:', projectId);

    // Test 2: List sessions/pages for the project
    console.log('\n2. Testing session list...');
    const sessionsResponse = await fetch(`${BASE_URL}/api/page_gen/session/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': SESSION_COOKIE
      },
      body: JSON.stringify({
        projectId: projectId.toString(),
        page: { pageNum: 1, pageSize: 30 }
      })
    });

    if (!sessionsResponse.ok) {
      throw new Error(`Session list failed: ${sessionsResponse.status}`);
    }

    const sessionsData = await sessionsResponse.json();
    console.log('✅ Sessions found:', sessionsData.sessions.length);

    if (sessionsData.sessions.length === 0) {
      console.log('❌ No sessions found. Please create a page first.');
      return;
    }

    const sessionId = sessionsData.sessions[0].id;
    const originalTitle = sessionsData.sessions[0].title;
    console.log('📄 Using session ID:', sessionId);
    console.log('📄 Original title:', originalTitle);

    // Test 3: Rename the session
    console.log('\n3. Testing session rename...');
    const newTitle = `Test Renamed Page ${Date.now()}`;
    const renameResponse = await fetch(`${BASE_URL}/api/page_gen/session/rename`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': SESSION_COOKIE
      },
      body: JSON.stringify({
        sessionId: sessionId,
        newTitle: newTitle
      })
    });

    if (!renameResponse.ok) {
      const errorText = await renameResponse.text();
      throw new Error(`Rename failed: ${renameResponse.status} - ${errorText}`);
    }

    const renameData = await renameResponse.json();
    console.log('✅ Rename response:', renameData);

    // Test 4: Verify the rename worked by listing sessions again
    console.log('\n4. Verifying rename...');
    const verifyResponse = await fetch(`${BASE_URL}/api/page_gen/session/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': SESSION_COOKIE
      },
      body: JSON.stringify({
        projectId: projectId.toString(),
        page: { pageNum: 1, pageSize: 30 }
      })
    });

    const verifyData = await verifyResponse.json();
    const renamedSession = verifyData.sessions.find(s => s.id === sessionId);
    
    if (renamedSession && renamedSession.title === newTitle) {
      console.log('✅ Rename verified! New title:', renamedSession.title);
    } else {
      console.log('❌ Rename verification failed. Current title:', renamedSession?.title);
    }

    // Test 5: Test delete (optional - uncomment to test)
    /*
    console.log('\n5. Testing session delete...');
    const deleteResponse = await fetch(`${BASE_URL}/api/page_gen/session/delete`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': SESSION_COOKIE
      },
      body: JSON.stringify({
        sessionId: sessionId
      })
    });

    if (!deleteResponse.ok) {
      const errorText = await deleteResponse.text();
      throw new Error(`Delete failed: ${deleteResponse.status} - ${errorText}`);
    }

    const deleteData = await deleteResponse.json();
    console.log('✅ Delete response:', deleteData);
    */

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Instructions for running the test
console.log(`
📋 Instructions:
1. Make sure the backend server is running on port 5000
2. Open browser dev tools and copy your session cookie
3. Replace 'your-session-cookie-here' with your actual session cookie
4. Run: node test_page_operations.js

🔧 To get your session cookie:
1. Open http://localhost:5173 in browser
2. Open Dev Tools (F12)
3. Go to Application/Storage tab
4. Find Cookies for localhost:5173
5. Copy the entire cookie string

Example cookie format:
connect.sid=s%3A...; other-cookie=value
`);

// Only run if session cookie is provided
if (SESSION_COOKIE !== 'your-session-cookie-here') {
  testPageOperations();
} else {
  console.log('⚠️  Please set your session cookie first!');
}
