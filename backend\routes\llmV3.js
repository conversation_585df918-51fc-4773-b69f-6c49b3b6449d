const express = require('express');
const router = express.Router();
const llmControllerV3 = require('../controllers/llmControllerV3');

/**
 * V3 LLM Routes - Clean implementation based on Readdy.ai approach
 */

// Step 1: Generate intent from element click (like Readdy's /api/page_gen/generate_intent)
router.post('/generate-intent', llmControllerV3.generateIntent);

// Step 2: Implement functionality (inline/modal/page) with server-side prompts
router.post('/implement', llmControllerV3.implementFeature);

// Generate complete HTML from prompt
router.post('/generate-html', llmControllerV3.generateHTML);

// Edit existing HTML with targeted changes (Readdy.ai style)
router.post('/edit', llmControllerV3.editHTML);

// Generate structured plan from prompt (for plan review page)
router.post('/plan', llmControllerV3.generatePlan);

// Generate streaming plan (for chat)
router.post('/plan/stream', llmControllerV3.generateStreamingPlan);

// Generate code from plan
router.post('/generate', llmControllerV3.generateCode);

module.exports = router;
